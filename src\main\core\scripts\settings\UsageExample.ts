import { 
    getBatterySettings, 
    getPackVoltageLimit, 
    getCycleCountLimit, 
    getTemperatureLimit,
    getCycleCountLevel,
    getLocationList,
    getBrokenReasonList
} from './index';
import { BatteryValidator } from '../validators/BatteryValidator';
import { BatteryData } from '../interfaces/batteryData';

/**
 * 全域設定使用範例
 * 展示如何在不同模組中使用電池檢測設定
 */

/**
 * 在 DiagnosticsView 中使用設定進行電池檢測
 */
export class DiagnosticsWithSettings {
    
    /**
     * 檢測電池是否符合標準
     * @param batteryData 電池數據
     * @returns 檢測結果
     */
    public static checkBatteryCompliance(batteryData: BatteryData): {
        isCompliant: boolean;
        issues: string[];
        recommendations: string[];
    } {
        const issues: string[] = [];
        const recommendations: string[] = [];
        
        // 使用全域設定進行檢測
        
        // 1. 電壓檢測
        if (!BatteryValidator.isVoltageNormal(batteryData.voltage)) {
            issues.push(`電壓 ${batteryData.voltage}mV 超出限制 ${getPackVoltageLimit()}mV`);
            recommendations.push("檢查電池充電狀態或更換電池");
        }
        
        // 2. 溫度檢測
        if (!BatteryValidator.isTemperatureNormal(batteryData.temperature)) {
            issues.push(`溫度 ${batteryData.temperature}°C 超出限制 ${getTemperatureLimit()}°C`);
            recommendations.push("等待電池冷卻或檢查散熱系統");
        }
        
        // 3. 循環次數檢測
        if (BatteryValidator.isCycleCountExceeded(batteryData.cycle)) {
            issues.push(`循環次數 ${batteryData.cycle} 超出限制 ${getCycleCountLimit()}`);
            recommendations.push("考慮更換電池");
        }
        
        // 4. 健康度等級檢測
        const healthPercentage = (batteryData.fullyChargedCapacity / batteryData.designCapacity) * 100;
        const healthLevel = BatteryValidator.getBatteryHealthLevel(healthPercentage);
        
        if (healthLevel >= 5) {
            issues.push(`電池健康度等級 ${healthLevel} (${healthPercentage.toFixed(1)}%)`);
            recommendations.push("電池健康度較低，建議進行維護或更換");
        }
        
        // 5. 隔離檢查
        const isolationCheck = BatteryValidator.checkBatteryIsolation(batteryData);
        if (isolationCheck.shouldIsolate) {
            issues.push("電池需要隔離");
            recommendations.push(...isolationCheck.reasons);
        }
        
        return {
            isCompliant: issues.length === 0,
            issues,
            recommendations
        };
    }
    
    /**
     * 生成電池狀態報告
     * @param batteryDataList 電池數據列表
     */
    public static generateStatusReport(batteryDataList: BatteryData[]): void {
        console.log("=== 電池狀態報告 (基於全域設定) ===");
        console.log(`檢測標準:`);
        console.log(`- Pack Voltage Limit: ${getPackVoltageLimit()}mV`);
        console.log(`- Cycle Count Limit: ${getCycleCountLimit()}`);
        console.log(`- Temperature Limit: ${getTemperatureLimit()}°C`);
        console.log("");
        
        batteryDataList.forEach((battery, index) => {
            const compliance = this.checkBatteryCompliance(battery);
            const status = compliance.isCompliant ? "✅ 合格" : "❌ 不合格";
            
            console.log(`電池 ${battery.id} (${battery.sn}): ${status}`);
            if (compliance.issues.length > 0) {
                console.log(`  問題: ${compliance.issues.join(", ")}`);
                console.log(`  建議: ${compliance.recommendations.join(", ")}`);
            }
        });
        
        console.log("=== 報告結束 ===");
    }
}

/**
 * 在 verify 功能中使用設定
 */
export class VerifyWithSettings {
    
    /**
     * 驗證電池是否可以使用
     * @param batteryData 電池數據
     * @returns 驗證結果
     */
    public static verifyBatteryUsability(batteryData: BatteryData): {
        canUse: boolean;
        reason: string;
        suggestedLocation?: string;
    } {
        // 1. 基本驗證
        const basicCheck = BatteryValidator.comprehensiveBatteryCheck(batteryData);
        
        if (!basicCheck.isValid) {
            return {
                canUse: false,
                reason: `基本檢測失敗: ${basicCheck.issues.join(", ")}`
            };
        }
        
        // 2. 隔離檢查
        if (basicCheck.shouldIsolate) {
            return {
                canUse: false,
                reason: `需要隔離: ${basicCheck.isolationReasons.join(", ")}`
            };
        }
        
        // 3. 損壞檢查
        if (BatteryValidator.isAppearanceBroken(batteryData.id.toString())) {
            return {
                canUse: false,
                reason: "電池外觀損壞"
            };
        }
        
        // 4. 根據健康度等級建議位置
        const locations = getLocationList();
        let suggestedLocation = locations[0]; // 預設位置
        
        if (basicCheck.healthLevel <= 2) {
            suggestedLocation = locations.find(loc => loc.includes("A")) || locations[0];
        } else if (basicCheck.healthLevel <= 4) {
            suggestedLocation = locations.find(loc => loc.includes("B")) || locations[1];
        } else {
            suggestedLocation = locations.find(loc => loc.includes("Maintenance")) || locations[2];
        }
        
        return {
            canUse: true,
            reason: "電池狀態良好，可以使用",
            suggestedLocation
        };
    }
    
    /**
     * 批量驗證電池
     * @param batteryDataList 電池數據列表
     */
    public static batchVerifyBatteries(batteryDataList: BatteryData[]): {
        usable: BatteryData[];
        unusable: BatteryData[];
        locationAssignments: { [batteryId: number]: string };
    } {
        const usable: BatteryData[] = [];
        const unusable: BatteryData[] = [];
        const locationAssignments: { [batteryId: number]: string } = {};
        
        batteryDataList.forEach(battery => {
            const verification = this.verifyBatteryUsability(battery);
            
            if (verification.canUse) {
                usable.push(battery);
                if (verification.suggestedLocation) {
                    locationAssignments[battery.id] = verification.suggestedLocation;
                }
            } else {
                unusable.push(battery);
                console.log(`電池 ${battery.id} 不可使用: ${verification.reason}`);
            }
        });
        
        return { usable, unusable, locationAssignments };
    }
}

/**
 * 設定變更監聽範例
 */
export class SettingsMonitor {
    private static instance: SettingsMonitor;
    
    private constructor() {
        this.setupSettingsListener();
    }
    
    public static getInstance(): SettingsMonitor {
        if (!SettingsMonitor.instance) {
            SettingsMonitor.instance = new SettingsMonitor();
        }
        return SettingsMonitor.instance;
    }
    
    /**
     * 設定設定變更監聽器
     */
    private setupSettingsListener(): void {
        const settings = getBatterySettings();
        
        settings.addListener((newSettings) => {
            console.log("電池設定已更新:", newSettings);
            
            // 可以在這裡觸發其他模組的更新
            this.notifyDiagnosticsUpdate();
            this.notifyVerifyUpdate();
        });
    }
    
    /**
     * 通知診斷模組設定已更新
     */
    private notifyDiagnosticsUpdate(): void {
        // 觸發診斷頁面重新檢測
        const event = new CustomEvent('batterySettingsUpdated', {
            detail: { module: 'diagnostics' }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * 通知驗證模組設定已更新
     */
    private notifyVerifyUpdate(): void {
        // 觸發驗證功能重新評估
        const event = new CustomEvent('batterySettingsUpdated', {
            detail: { module: 'verify' }
        });
        document.dispatchEvent(event);
    }
}

/**
 * 使用範例
 */
export function demonstrateUsage(): void {
    console.log("=== 全域設定使用範例 ===");
    
    // 1. 直接存取設定值
    console.log("當前設定值:");
    console.log(`Pack Voltage Limit: ${getPackVoltageLimit()}mV`);
    console.log(`Cycle Count Limit: ${getCycleCountLimit()}`);
    console.log(`Temperature Limit: ${getTemperatureLimit()}°C`);
    console.log(`Health Level 1 Threshold: ${getCycleCountLevel(1)}%`);
    
    // 2. 獲取列表設定
    console.log("\n可用位置:");
    getLocationList().forEach((location, index) => {
        console.log(`${index + 1}. ${location}`);
    });
    
    console.log("\n損壞原因:");
    getBrokenReasonList().forEach((reason, index) => {
        console.log(`${index + 1}. ${reason}`);
    });
    
    // 3. 啟動設定監聽器
    SettingsMonitor.getInstance();
    
    console.log("\n設定監聽器已啟動，當設定變更時會自動通知相關模組。");
}
