/**
 * 設定模組導出文件
 * 統一導出所有設定相關的類和介面
 */
import { BatterySettings } from './BatterySettings';

// 導出設定管理類和介面
export { BatterySettings } from './BatterySettings';
export type { BatteryDetectionSettings } from './BatterySettings';

// 導出設定 UI 管理類
export { BatterySettingsUI } from './BatterySettingsUI';

// 提供便捷的全域存取方法
export const getBatterySettings = () => BatterySettings.getInstance();

// 常用的設定存取方法
export const getPackVoltageLimit = () => BatterySettings.getInstance().packVoltageLimit;
export const getCycleCountLimit = () => BatterySettings.getInstance().cycleCountLimit;
export const getTemperatureLimit = () => BatterySettings.getInstance().temperatureLimit;
export const getCellVoltage = () => BatterySettings.getInstance().cellVoltage;
export const getUnbalanceVoltage = () => BatterySettings.getInstance().unbalanceVoltage;

export const getIsolateCycle = () => BatterySettings.getInstance().isolateCycle;
export const getIsolateHealth = () => BatterySettings.getInstance().isolateHealth;
export const getIsolateYearTime = () => BatterySettings.getInstance().isolateYearTime;

export const getLocationList = () => BatterySettings.getInstance().locationList;
export const getBrokenReasonList = () => BatterySettings.getInstance().brokenReason;

// 循環計數等級存取方法
export const getCycleCountLevel = (level: 1|2|3|4|5|6): number => {
    const settings = BatterySettings.getInstance();
    switch (level) {
        case 1: return settings.cycleCountLevel1;
        case 2: return settings.cycleCountLevel2;
        case 3: return settings.cycleCountLevel3;
        case 4: return settings.cycleCountLevel4;
        case 5: return settings.cycleCountLevel5;
        case 6: return settings.cycleCountLevel6;
        default: return 0;
    }
};

// 導出使用範例（可選，用於參考和測試）
export {
    DiagnosticsWithSettings,
    VerifyWithSettings,
    SettingsMonitor,
    demonstrateUsage
} from './UsageExample';
