# Battery Validator

電池驗證器模組，提供電池序列號驗證功能。

## 功能特色

- 驗證電池序列號格式和有效性
- 支援 XL 和 Standard 兩種電池類型
- 可選擇是否排除特定日期 (1803, 1804)
- **電池損壞狀態管理**（localStorage 持久化儲存）
- 批量驗證和損壞檢測功能
- 提供多種實用的輔助方法
- 包含完整的測試用例和使用範例
- 可與現有 DiagnosticsView 無縫整合

## 安裝和使用

### 基本使用

```typescript
import { BatteryValidator } from './validators/BatteryValidator';

// 驗證序列號
const isValid = BatteryValidator.isSerialNumberValid("9789B-1805");
console.log(isValid); // true

// 不檢查排除日期
const isValidNoDateCheck = BatteryValidator.isSerialNumberValid("9789B-1803", false);
console.log(isValidNoDateCheck); // true

// 獲取電池類型
const batteryType = BatteryValidator.getBatteryTypeFromSerial("9789B-1805");
console.log(batteryType); // "XL"
```

### 進階使用

```typescript
import {
    validateBatteryData,
    generateBatteryValidationReport,
    filterValidBatteries,
    checkBatteryDamage,
    setBatteryDamageStatus,
    generateBatteryDamageReport
} from './validators';
import { BatteryData } from '../interfaces/batteryData';

// 驗證單個電池數據
const batteryData: BatteryData = {
    id: 1,
    sn: "9789B-1805",
    // ... 其他屬性
};

const validation = validateBatteryData(batteryData);
console.log(validation.validationMessage);

// 批量驗證和過濾
const batteryList: BatteryData[] = [/* ... */];
const validBatteries = filterValidBatteries(batteryList);
const report = generateBatteryValidationReport(batteryList);

// 電池損壞檢測
const isDamaged = checkBatteryDamage("1");
setBatteryDamageStatus("1", true); // 設定電池 1 為損壞
const damageReport = generateBatteryDamageReport(batteryList);
```

## API 文檔

### BatteryValidator 類

#### 靜態方法

##### `isSerialNumberValid(serial: string, check1803to04?: boolean): boolean`

驗證電池序列號是否有效。

**參數：**
- `serial`: 電池序列號
- `check1803to04`: 是否檢查並排除 1803 和 1804 日期（預設：true）

**返回值：**
- `boolean`: 序列號是否有效

**範例：**
```typescript
BatteryValidator.isSerialNumberValid("9789B-1805"); // true
BatteryValidator.isSerialNumberValid("9789B-1803"); // false
BatteryValidator.isSerialNumberValid("9789B-1803", false); // true
```

##### `getBatteryTypeFromSerial(serial: string): string`

根據序列號獲取電池類型。

**參數：**
- `serial`: 電池序列號

**返回值：**
- `string`: 電池類型 ("XL", "Standard", "Unknown")

##### `isSerialFormatValid(serial: string): boolean`

檢查序列號格式是否正確（不檢查前綴）。

##### `extractDateFromSerial(serial: string): string`

從序列號中提取日期部分。

##### `isExcludedDate(dateStr: string): boolean`

檢查日期是否為被排除的日期。

### 電池損壞檢測 API

##### `getBatteryDamage(batteryId: string): boolean`

獲取特定電池的損壞狀態。

**參數：**
- `batteryId`: 電池 ID

**返回值：**
- `boolean`: 是否損壞

##### `setBatteryDamage(batteryId: string, isDamaged: boolean): void`

設定特定電池的損壞狀態。

**參數：**
- `batteryId`: 電池 ID
- `isDamaged`: 是否損壞

##### `getBatteryDamageInfo(): { [batteryId: string]: boolean }`

獲取所有電池的損壞狀態資訊。

##### `saveBatteryDamageInfo(damageInfo: { [batteryId: string]: boolean }): void`

儲存電池損壞狀態資訊到 localStorage。

##### `getBatteryDamageStatus(batteryIds: string[]): { [batteryId: string]: boolean }`

批量獲取多個電池的損壞狀態。

##### `clearAllBatteryDamageInfo(): void`

清除所有電池的損壞狀態記錄。

##### `getDamagedBatteryIds(): string[]`

獲取所有損壞的電池 ID 列表。

##### `isAppearanceBroken(batteryId: string): boolean`

驗證電池外觀是否損壞（基於 localStorage 儲存的狀態）。

## 驗證規則

### 電池類型識別

- **XL 類型**: 序列號以 "9789B" 開頭
- **Standard 類型**: 序列號以 "17500" 開頭
- **Unknown 類型**: 不符合上述前綴的序列號

### 格式要求

序列號必須符合正則表達式：`^[a-zA-Z0-9]*-[a-zA-Z0-9]*$`

即：字母數字組合 + 連字符 + 字母數字組合

### 日期排除規則

當 `check1803to04` 參數為 `true` 時（預設值），會排除以下日期：
- 1803
- 1804

日期是從序列號中連字符後的前 4 個字符提取。

## 測試

執行測試：

```typescript
import { runBatteryValidatorTests, runHelperMethodsTests } from './validators';

// 執行主要驗證測試
runBatteryValidatorTests();

// 執行輔助方法測試
runHelperMethodsTests();
```

## 範例

### 有效的序列號

- `9789B-1805` (XL 類型)
- `9789BTEST-1806` (XL 類型，較長前綴)
- `17500-1805` (Standard 類型)
- `17500ABC-1807` (Standard 類型，較長前綴)

### 無效的序列號

- `9789B-1803` (排除日期)
- `9789B-1804` (排除日期)
- `9789B1805` (缺少連字符)
- `9789B-` (缺少日期部分)
- `INVALID-1805` (無效前綴)
- `9789B@-1805` (包含特殊字符)

## 注意事項

1. 此驗證器是從 C# 版本轉換而來，保持了原有的邏輯和行為
2. 所有方法都是靜態方法，不需要實例化類
3. 驗證器專注於序列號格式和規則，不涉及其他電池屬性的驗證
4. 建議在生產環境中使用前先執行測試用例確保功能正常

## 與現有項目整合

### 替換 DiagnosticsView.ts 中的函數

您可以使用共用驗證器替換 DiagnosticsView.ts 中的電池損壞相關函數：

```typescript
// 在 DiagnosticsView.ts 頂部添加導入
import {
    getBatteryDamage,
    setBatteryDamage,
    getBatteryDamageInfo,
    saveBatteryDamageInfo
} from '../validators/DiagnosticsIntegrationExample';

// 移除原有的這些函數定義：
// - getBatteryDamage
// - setBatteryDamage
// - getBatteryDamageInfo
// - saveBatteryDamageInfo
// - BatteryDamageInfo interface

// 現在可以直接使用導入的函數
```

### 在 verify 功能中使用

```typescript
import { BatteryValidator, checkBatteryDamage } from '../validators';

// 驗證序列號
const isValidSerial = BatteryValidator.isSerialNumberValid(serialNumber);

// 檢查損壞狀態
const isDamaged = checkBatteryDamage(batteryId);

// 綜合驗證
if (isValidSerial && !isDamaged) {
    console.log("電池驗證通過");
} else {
    console.log("電池驗證失敗");
}
```

## 更新日誌

- v1.1.0: 添加電池損壞檢測功能，支援 localStorage 持久化儲存
- v1.0.0: 初始版本，從 C# IsSerialNumberValid 方法轉換而來
- 包含完整的驗證邏輯、測試用例和使用範例
