import { batteryDataHelper } from '../interfaces/batteryData';
import { BatteryValidator } from './BatteryValidator';

/**
 * 電池驗證器測試
 * 這個文件包含了對 BatteryValidator 類的測試用例
 */

// 測試用例數據
const testCases = [
    // XL 類型測試
    { serial: "9789B-1805", expected: true, description: "XL type with valid date" },
    { serial: "9789B-1803", expected: false, description: "XL type with excluded date 1803" },
    { serial: "9789B-1804", expected: false, description: "XL type with excluded date 1804" },
    { serial: "9789BTEST-1805", expected: true, description: "XL type with longer prefix" },
    
    // Standard 類型測試
    { serial: "17500-1805", expected: true, description: "Standard type with valid date" },
    { serial: "17500-1803", expected: false, description: "Standard type with excluded date 1803" },
    { serial: "17500-1804", expected: false, description: "Standard type with excluded date 1804" },
    { serial: "17500ABC-1805", expected: true, description: "Standard type with longer prefix" },
    
    // 格式測試
    { serial: "9789B1805", expected: false, description: "Missing dash separator" },
    { serial: "9789B-", expected: false, description: "Missing date part" },
    { serial: "-1805", expected: false, description: "Missing prefix part" },
    { serial: "9789B-18", expected: false, description: "Incomplete date" },
    
    // 無效前綴測試
    { serial: "INVALID-1805", expected: false, description: "Invalid prefix" },
    { serial: "1234-1805", expected: false, description: "Numeric prefix not matching patterns" },
    
    // 特殊字符測試
    { serial: "9789B@-1805", expected: false, description: "Special character in prefix" },
    { serial: "9789B-18@5", expected: false, description: "Special character in date" },
];

/**
 * 執行所有測試用例
 */
export function runBatteryValidatorTests(): void {
    console.log("=== 開始執行 BatteryValidator 測試 ===");
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        const result = BatteryValidator.isSerialNumberValid(testCase.serial);
        const passed = result === testCase.expected;
        
        console.log(`測試 ${index + 1}: ${testCase.description}`);
        console.log(`  序列號: ${testCase.serial}`);
        console.log(`  預期結果: ${testCase.expected}`);
        console.log(`  實際結果: ${result}`);
        console.log(`  狀態: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        console.log('---');
        
        if (passed) {
            passedTests++;
        }
    });
    
    console.log(`=== 測試完成 ===`);
    console.log(`通過: ${passedTests}/${totalTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // 測試不檢查日期的情況
    console.log("\n=== 測試不檢查排除日期的情況 ===");
    const excludedDateTests = [
        { serial: "9789B-1803", expected: true, description: "XL type with 1803 (no date check)" },
        { serial: "9789B-1804", expected: true, description: "XL type with 1804 (no date check)" },
        { serial: "17500-1803", expected: true, description: "Standard type with 1803 (no date check)" },
        { serial: "17500-1804", expected: true, description: "Standard type with 1804 (no date check)" },
    ];
    
    excludedDateTests.forEach((testCase, index) => {
        const result = BatteryValidator.isSerialNumberValid(testCase.serial, false);
        const passed = result === testCase.expected;
        
        console.log(`測試 ${index + 1}: ${testCase.description}`);
        console.log(`  序列號: ${testCase.serial}`);
        console.log(`  預期結果: ${testCase.expected}`);
        console.log(`  實際結果: ${result}`);
        console.log(`  狀態: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        console.log('---');
    });
}

/**
 * 測試其他輔助方法
 */
export function runHelperMethodsTests(): void {
    console.log("\n=== 測試輔助方法 ===");
    
    // 測試 getBatteryTypeFromSerial
    console.log("測試 getBatteryTypeFromSerial:");
    console.log(`9789B-1805 -> ${batteryDataHelper.getBatteryTypeText("9789B-1805")}`);
    console.log(`17500-1805 -> ${batteryDataHelper.getBatteryTypeText("17500-1805")}`);
    console.log(`UNKNOWN-1805 -> ${batteryDataHelper.getBatteryTypeText("UNKNOWN-1805")}`);
    
    // 測試 isSerialFormatValid
    console.log("\n測試 isSerialFormatValid:");
    console.log(`9789B-1805 -> ${BatteryValidator.isSerialFormatValid("9789B-1805")}`);
    console.log(`9789B1805 -> ${BatteryValidator.isSerialFormatValid("9789B1805")}`);
    console.log(`9789B- -> ${BatteryValidator.isSerialFormatValid("9789B-")}`);
    
    // 測試 extractDateFromSerial
    console.log("\n測試 extractDateFromSerial:");
    console.log(`9789B-1805 -> ${BatteryValidator.extractDateFromSerial("9789B-1805")}`);
    console.log(`17500-1803 -> ${BatteryValidator.extractDateFromSerial("17500-1803")}`);
    console.log(`INVALID -> ${BatteryValidator.extractDateFromSerial("INVALID")}`);
    
    // 測試 isExcludedDate
    console.log("\n測試 isExcludedDate:");
    console.log(`1803 -> ${BatteryValidator.isExcludedDate("1803")}`);
    console.log(`1804 -> ${BatteryValidator.isExcludedDate("1804")}`);
    console.log(`1805 -> ${BatteryValidator.isExcludedDate("1805")}`);
}

// 如果直接執行此文件，則運行測試
if (typeof window !== 'undefined') {
    // 瀏覽器環境
    (window as any).runBatteryValidatorTests = runBatteryValidatorTests;
    (window as any).runHelperMethodsTests = runHelperMethodsTests;
}
