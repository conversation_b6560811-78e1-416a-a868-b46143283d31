/**
 * 電池驗證類
 * 提供電池相關的驗證功能
 */
export class BatteryValidator {
    /**
     * 驗證電池序列號是否有效
     * 
     * 驗證規則：
     * 1. XL 類型：序列號以 "9789B" 開頭
     * 2. Standard 類型：序列號以 "17500" 開頭
     * 3. 格式必須符合正則表達式：^[a-zA-Z0-9]*-[a-zA-Z0-9]*$
     * 4. 可選擇是否排除特定 SN (1803, 1804)
     * 
     * @param serial 電池序列號
     * @param check1803to04 是否檢查並排除 SN 為 1803 和 1804，預設為 true
     * @returns 序列號是否有效
     */
    public static isSerialNumberValid(serial: string, check1803to04: boolean = true): boolean {
        let ret = false;
        
        // 正則表達式：檢查格式是否為 字母數字-字母數字
        const regex = /^[a-zA-Z0-9]*-[a-zA-Z0-9]*$/;

        // XL 類型檢查
        if (serial.startsWith("9789B")) {
            ret = true;
        }
        // Standard 類型檢查
        else if (serial.startsWith("17500")) {
            ret = true;
        }

        // 檢查是否符合正則表達式格式
        if (regex.test(serial)) {
            if (check1803to04) {
                // 找到 '-' 的位置
                const dashIndex = serial.indexOf('-');
                if (dashIndex !== -1 && dashIndex + 5 <= serial.length) {
                    // 提取 '-' 後的 4 個字符作為日期
                    const dateInSN = serial.substring(dashIndex + 1, dashIndex + 5);
                    
                    // 如果 SN 不是 1803 或 1804，則有效
                    if (dateInSN !== "1803" && dateInSN !== "1804") {
                        ret = true;
                    } else {
                        ret = false;
                    }
                } else {
                    // 如果格式不正確（沒有足夠的字符），則無效
                    ret = false;
                }
            } else {
                // 不檢查特定日期，只要格式正確就有效
                ret = true;
            }
        } else {
            // 格式不符合正則表達式，無效
            ret = false;
        }

        return ret;
    }

    /**
     * 驗證電池外殼是否損壞
     * @param batteryData 電池數據
     * @returns 驗證結果
     */
    public static IsAppearanceBroken(batteryData: BatteryData): boolean
    {
        let ret = false;
        if (batteryData.isDamaged == true)
            ret = false;
        else if (batteryData.isDamaged == true)
            ret = true;
    
        return ret;
    }

    /**
     * 獲取電池類型基於序列號
     * @param serial 電池序列號
     * @returns 電池類型字符串
     */
    public static getBatteryTypeFromSerial(serial: string): string {
        if (serial.startsWith("9789B")) {
            return "XL";
        } else if (serial.startsWith("17500")) {
            return "Standard";
        } else {
            return "Unknown";
        }
    }

    /**
     * 驗證序列號格式是否正確（不檢查前綴）
     * @param serial 電池序列號
     * @returns 格式是否正確
     */
    public static isSerialFormatValid(serial: string): boolean {
        const regex = /^[a-zA-Z0-9]*-[a-zA-Z0-9]*$/;
        return regex.test(serial);
    }

    /**
     * 從序列號中提取日期部分
     * @param serial 電池序列號
     * @returns 日期字符串，如果無法提取則返回空字符串
     */
    public static extractDateFromSerial(serial: string): string {
        const dashIndex = serial.indexOf('-');
        if (dashIndex !== -1 && dashIndex + 5 <= serial.length) {
            return serial.substring(dashIndex + 1, dashIndex + 5);
        }
        return "";
    }

    /**
     * 檢查日期是否為被排除的日期 (1803, 1804)
     * @param dateStr 日期字符串
     * @returns 是否為被排除的日期
     */
    public static isExcludedDate(dateStr: string): boolean {
        return dateStr === "1803" || dateStr === "1804";
    }
}
